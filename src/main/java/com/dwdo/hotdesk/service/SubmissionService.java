package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.FileDTO;
import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.MainFile;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SupportingFiles;
import com.dwdo.hotdesk.repository.MainFileRepository;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SupportingFilesRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import com.dwdo.hotdesk.service.storage.CloudStorageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionService {

    private static final String STATUS_PENDING = "Pending";

    private final PaymentValidationService validationService;
    private final ApprovalService approvalService;

    @Qualifier("staggeredStorageService")
    private final CloudStorageService storageService;

    private final PaymentSubmissionRepository submissionRepository;
    private final MainFileRepository mainFileRepository;
    private final SupportingFilesRepository supportingFilesRepository;

    @Autowired
    private EmployeeClient employeeClient;

    @Transactional
    public List<GeneralBodyResponse> bulkSubmission(
            MultipartFile mainFile,
            List<MultipartFile> supportingFiles) {

        log.info("[SubmissionService] Processing submission request");

        String submitterNIP = getAuthenticatedUserNIP();
        EpiccEmployeeProfileImpl employeeDetails = getEmployeeDetails(submitterNIP);
        String submitterName = employeeDetails.getLastName();
        String submitterJob = getSubmitterJob();

        log.info("[SubmissionService] Processing submission from {} ({})", submitterName, submitterNIP);

        List<PaymentDataDTO> validatedData = validateMainFile(mainFile);
        if (validatedData == null || validatedData.isEmpty()) {
            return Collections.emptyList();
        }

        log.info("[SubmissionService] Main file validated successfully with {} payment records", validatedData.size());

        String datetime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy_HHmmss"));
        List<GeneralBodyResponse> responses = new ArrayList<>();

        String batchId = UUID.randomUUID().toString().substring(0, 8);

        MainFile mainFileEntity = storeMainFile(mainFile, submitterNIP, datetime, batchId);
        if (mainFileEntity == null) {
            addErrorResponse(responses, null, "Failed to store main file");
            return responses;
        }

        List<SupportingFiles> supportingFilesEntities = new ArrayList<>();
        if (supportingFiles != null && !supportingFiles.isEmpty()) {
            supportingFilesEntities = storeSupportingFiles(supportingFiles, submitterNIP, datetime, batchId);
            log.info("[SubmissionService] Stored {} supporting files for batch", supportingFilesEntities.size());
        }

        List<Submission> submissions = new ArrayList<>();
        for (PaymentDataDTO data : validatedData) {
            try {
                normalizePaymentData(data);

                String referenceNumber = generateReferenceNumber();

                Submission submission = createSubmission(mainFileEntity, referenceNumber,
                        submitterName, submitterJob, data);

                submission = submissionRepository.save(submission);

                if (!supportingFilesEntities.isEmpty()) {
                    submission.getSupportingFiles().addAll(supportingFilesEntities);
                    submission = submissionRepository.save(submission);
                    log.debug("[SubmissionService] Associated {} supporting files with submission {}",
                            supportingFilesEntities.size(), submission.getReferenceNumber());
                }

                submissions.add(submission);

                responses.add(GeneralBodyResponse.builder()
                        .code(200)
                        .status("OK")
                        .message("Submission received successfully for " + submission.getNip())
                        .build());

            } catch (Exception e) {
                log.error("Error creating submission for {}", data.getNip(), e);
                addErrorResponse(responses, data, "Error creating submission: " + e.getMessage());
            }
        }

        List<String> userRoles = SecurityUtil.getCurrentUserRole();
        approvalService.updateStatus(submissions, submitterNIP, submitterName, userRoles);

        return responses;
    }

    @Transactional
    public GeneralBodyResponse singleSubmission(
            SingleValidRequestDTO requestData,
            List<MultipartFile> supportingFiles) {

        log.info("[SubmissionService] Processing single submission for NIP: {}", requestData.getNip());

        try {
            String submitterNIP = getAuthenticatedUserNIP();
            EpiccEmployeeProfileImpl employeeDetails = getEmployeeDetails(submitterNIP);
            String submitterName = employeeDetails.getLastName();
            String submitterJob = getSubmitterJob();

            log.info("[SubmissionService] Validating payment data for NIP: {}", requestData.getNip());

            GeneralBodyResponse validationResponse = validationService.validateSinglePayment(requestData);
            if (validationResponse.getCode() != 200) {
                log.error("[SubmissionService] Single payment validation failed: {}", validationResponse.getMessage());
                return validationResponse;
            }

            PaymentDataDTO data = (PaymentDataDTO) validationResponse.getData();
            normalizePaymentData(data);

            String datetime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("dd-MM-yyyy_HHmmss"));
            String referenceNumber = generateReferenceNumber();

            Submission submission = createSubmission(
                    null,
                    referenceNumber,
                    submitterName,
                    submitterJob,
                    data);
            
            submission = submissionRepository.save(submission);

            if (supportingFiles != null && !supportingFiles.isEmpty()) {
                String batchId = UUID.randomUUID().toString().substring(0, 8);
                List<SupportingFiles> supportingFilesEntities = storeSupportingFiles(
                        supportingFiles, submitterNIP, datetime, batchId);

                if (!supportingFilesEntities.isEmpty()) {
                    submission.getSupportingFiles().addAll(supportingFilesEntities);
                    submission = submissionRepository.save(submission);
                    log.debug("[SubmissionService] Associated {} supporting files with single submission {}",
                            supportingFilesEntities.size(), submission.getReferenceNumber());
                }
            }

            GeneralBodyResponse response = GeneralBodyResponse.builder()
                    .code(200)
                    .status("OK")
                    .message("Submission received successfully for " + data.getNip())
                    .build();

            List<Submission> singleSubmissionList = Collections.singletonList(submission);
            approvalService.updateStatus(singleSubmissionList, submitterNIP, submitterName);

            return response;

        } catch (CustomBadRequestException e) {
            log.error("[SubmissionService] Bad request error for {}: {}", requestData.getNip(), e.getMessage());
            return GeneralBodyResponse.builder()
                    .code(e.getCode())
                    .status(e.getStatus())
                    .message(e.getMessage())
                    .build();
        } catch (Exception e) {
            log.error("[SubmissionService] Error processing submission for {}: {}",
                    requestData.getNip(), e.getMessage(), e);

            return GeneralBodyResponse.builder()
                    .code(500)
                    .status("Error")
                    .message("Error processing submission: " + e.getMessage() + " for " + requestData.getNip())
                    .build();
        }
    }


    private String generateReferenceNumber() {
        String timeComponent = String.valueOf(System.currentTimeMillis()).substring(9, 13);
        String randomComponent = UUID.randomUUID().toString().substring(0, 4).toUpperCase();
        String referenceNumber = "SP-" + timeComponent + randomComponent;

        if (submissionRepository.countByReferenceNumber(referenceNumber) > 0) {
            return generateReferenceNumber();
        }

        return referenceNumber;
    }

    private String getAuthenticatedUserNIP() {
        return SecurityUtil.getCurrentUserLogin()
                .orElseThrow(() -> new CustomBadRequestException(401, "Authentication Error",
                        "User not authenticated or NIP not available"));
    }

    private EpiccEmployeeProfileImpl getEmployeeDetails(String nip) {
        ResponseEntity<EpiccEmployeeProfileImpl> response = employeeClient.getProfile(nip);
        if (response == null || response.getBody() == null) {
            throw new CustomBadRequestException(404, "Employee Not Found",
                    "Could not retrieve employee details for NIP: " + nip);
        }
        return response.getBody();
    }

    private List<PaymentDataDTO> validateMainFile(MultipartFile mainFile) {
        GeneralBodyResponse validationResponse = validationService.validateExcelFile(mainFile);
        if (validationResponse.getCode() != 200) {
            log.error("[SubmissionService] Main file validation failed: {}", validationResponse.getMessage());
            return Collections.emptyList();
        }
        return (List<PaymentDataDTO>) validationResponse.getData();
    }

    private void normalizePaymentData(PaymentDataDTO data) {
        if (data.getSlik() == null) data.setSlik("-");
        if (data.getSanction() == null) data.setSanction("-");
        if (data.getTerminationDate() == null) data.setTerminationDate("-");
    }

    private MainFile storeMainFile(MultipartFile file, String submitterNIP, String datetime, String referenceNumber) {
        String mainFilePath = String.format("Staggered Payment/%s/%s/%s/%s",
                submitterNIP, datetime, referenceNumber, file.getOriginalFilename());

        FileDTO storedMainFile = storageService.storeFile(mainFilePath, file);
        if (storedMainFile == null) {
            log.error("[SubmissionService] Failed to store main file");
            return null;
        }

        MainFile mainFileEntity = MainFile.builder()
                .fileName(file.getOriginalFilename())
                .filePath(mainFilePath)
                .fileType(file.getContentType())
                .fileSize(file.getSize())
                .build();

        return mainFileRepository.save(mainFileEntity);
    }

    private Submission createSubmission(
            MainFile mainFileEntity,
            String referenceNumber,
            String submitterName,
            String submitterJob,
            PaymentDataDTO data) {

        Submission submission = Submission.builder()
                .mainFile(mainFileEntity)
                .taskName("")
                .referenceNumber(referenceNumber)
                .submitterName(submitterName)
                .submitterJob(submitterJob)
                .currentReviewer("")
                .status(STATUS_PENDING)
                // Payment details from the Excel row
                .nip(data.getNip())
                .name(data.getName())
                .grade(data.getGrade())
                .paymentType(data.getPaymentType())
                .amount(data.getAmount())
                .description(data.getDescription())
                .monthOfProcess(data.getMonthOfProcess())
                .yearOfProcess(data.getYearOfProcess())
                .directorate(data.getDirectorate())
                .slik(data.getSlik())
                .sanction(data.getSanction())
                .terminationDate(data.getTerminationDate())
                .eligible(data.getEligible())
                .supportingFiles(new ArrayList<>())
                .build();

        return submissionRepository.save(submission);
    }

    private List<SupportingFiles> storeSupportingFiles(
            List<MultipartFile> supportingFiles,
            String submitterNIP,
            String datetime,
            String referenceNumber) {

        List<SupportingFiles> supportingFilesEntities = new ArrayList<>();

        for (MultipartFile supportFile : supportingFiles) {
            String supportFilePath = String.format("Staggered Payment/%s/%s/%s/%s",
                    submitterNIP, datetime, referenceNumber, supportFile.getOriginalFilename());

            FileDTO storedSupportFile = storageService.storeFile(supportFilePath, supportFile);
            if (storedSupportFile == null) {
                log.warn("Failed to store supporting file: {}", supportFile.getOriginalFilename());
                continue;
            }

            SupportingFiles supportingFile = SupportingFiles.builder()
                    .fileName(supportFile.getOriginalFilename())
                    .filePath(supportFilePath)
                    .fileType(supportFile.getContentType())
                    .fileSize(supportFile.getSize())
                    .build();

            supportingFilesEntities.add(supportingFilesRepository.save(supportingFile));
        }

        return supportingFilesEntities;
    }

    private void addErrorResponse(List<GeneralBodyResponse> responses, PaymentDataDTO data, String errorMessage) {
        responses.add(GeneralBodyResponse.builder()
                .code(500)
                .status("Error")
                .message(errorMessage + (data != null ? " for " + data.getNip() : ""))
                .build());
    }

    private String getSubmitterJob() {
        List<String> roles = SecurityUtil.getCurrentUserRole();
        for (String role : roles) {
            if (role.equals("ROLE_ADMIN_HRBP") ||
                role.equals("ROLE_ADMIN_HRPAYROLL") ||
                role.equals("ROLE_ADMIN_HRPAYROLL_HEAD") ||
                role.equals("ROLE_ADMIN_HRREWARD")) {
                if (role.equals("ROLE_ADMIN_HRPAYROLL_HEAD")) {
                    return "HRPAYROLLHEAD";
                }
                return role.replace("ROLE_ADMIN_", "");
            }
        }
        throw new CustomBadRequestException(403, "Forbidden",
                "User does not have the required role to submit payments");
    }

}
