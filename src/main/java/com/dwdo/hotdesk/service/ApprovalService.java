package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.service.feign.request.SubmitTransactionRequest;
import com.dwdo.hotdesk.service.feign.request.TransactionActionRequest;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.DetailResponse;
import com.dwdo.hotdesk.security.SecurityUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApprovalService {

    private static final String STATUS_WAITING_APPROVAL = "Waiting for Approval";
    private static final String STATUS_WAITING_PAYMENT_APPROVAL = "Waiting for Payment Approval";
    private static final String STATUS_WAITING_PAYMENT = "Waiting for Payment";
    private static final String STATUS_PAID = "Paid";
    private static final String STATUS_UNPAID = "Unpaid";
    private static final String STATUS_PENDING = "Pending";
    private static final String STATUS_FAILED = "Failed";
    private static final String STATUS_DECLINED = "Declined";
    private static final String STATUS_NEED_REVISION = "Need Revision";
    private static final String STATUS_CANCELLED = "Cancelled";

    private static final String ACTION_APPROVE = "APPROVE";
    private static final String ACTION_REJECT = "REJECT";
    private static final String ACTION_REVISE = "REVISE";
    private static final String ACTION_CANCEL = "CANCEL";

    private static final String KEY_TASK_NAME = "taskName";
    private static final String KEY_ACTION = "action";
    private static final String KEY_STATUS = "status";
    private static final String KEY_ERROR = "error";
    private static final String KEY_SUCCESS = "success";


    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionPaymentRepository submissionPaymentRepository;
    private final ApprovalClient approvalClient;

    @Autowired
    @Lazy
    private ApprovalService service;

    @Value("${rule.code.staggered-payment}")
    private String staggeredPaymentRuleCode;

    @Value("${system.code.staggered-payment}")
    private String staggeredPaymentSystemCode;

    @Value("${process.name.staggered-payment}")
    private String staggeredPaymentProcessName;


    public void processAction(List<String> taskNames, String action, String reason,
                             String monthOfProcess, String yearOfProcess, String paymentType, BigDecimal amount) {
        String currentUserNip = SecurityUtil.getCurrentUserLogin()
                .orElseThrow(() -> new CustomBadRequestException(401, "Authentication Error",
                        "User not authenticated or NIP not available"));

        log.info("[Action] Starting ASYNC processing of {} task(s) with action '{}' by user: {}, monthOfProcess: {}, yearOfProcess: {}, paymentType: {}, amount: {}",
                taskNames.size(), action, currentUserNip, monthOfProcess, yearOfProcess, paymentType, amount);

        service.processActionAsync(taskNames, action, reason, currentUserNip, monthOfProcess, yearOfProcess, paymentType, amount);
    }

    @Async
    @Transactional
    public void processActionAsync(List<String> taskNames, String action, String reason, String currentUserNip,
                                  String monthOfProcess, String yearOfProcess, String paymentType, BigDecimal amount) {
        log.info("[Action] Starting ASYNC TRANSACTIONAL processing of {} task(s) with action '{}' by user: {}",
                taskNames.size(), action, currentUserNip);

        try {
            if (monthOfProcess != null || yearOfProcess != null || paymentType != null || amount != null) {
                validateTasksForAction(taskNames);
                updateTasksWithOptionalFields(taskNames, monthOfProcess, yearOfProcess, paymentType, amount);
            }
            setSubmissionsStatusToPending(taskNames, reason);
            processActionInternal(taskNames, action, reason, currentUserNip);

            log.info("[Action] ✅ ASYNC TRANSACTIONAL processing completed successfully for {} task(s) - TRANSACTION WILL BE COMMITTED",
                    taskNames.size());

        } catch (Exception e) {
            log.error("[Action] ❌ Error in async transactional processing - TRANSACTION WILL BE ROLLED BACK", e);
            throw e;
        }
    }

    private void validateTasksForAction(List<String> taskNames) {
        log.info("[Validation] Checking if tasks have STATUS_NEED_REVISION before processing action with optional fields");

        List<String> invalidTasks = new ArrayList<>();

        for (String taskName : taskNames) {
            Optional<Submission> submissionOpt = submissionRepository.findByTaskName(taskName);
            if (submissionOpt.isEmpty()) {
                log.warn("[Validation] No submission found for task: {}", taskName);
                invalidTasks.add(taskName + " (not found)");
                continue;
            }

            Submission submission = submissionOpt.get();
            if (!STATUS_NEED_REVISION.equals(submission.getStatus())) {
                log.warn("[Validation] Task {} does not have status '{}'. Current status: {}",
                    taskName, STATUS_NEED_REVISION, submission.getStatus());
                invalidTasks.add(taskName + " (status: " + submission.getStatus() + ")");
            } else {
                log.info("[Validation] ✓ Task {} has valid status: {}", taskName, submission.getStatus());
            }
        }

        if (!invalidTasks.isEmpty()) {
            String errorMessage = "The following tasks do not have status '" + STATUS_NEED_REVISION + "': " +
                String.join(", ", invalidTasks);
            throw new CustomBadRequestException(400, "Invalid Status", errorMessage);
        }

        log.info("[Validation] All {} task(s) have valid status for action processing with optional fields", taskNames.size());
    }

    private void updateTasksWithOptionalFields(List<String> taskNames, String monthOfProcess, String yearOfProcess,
                                              String paymentType, BigDecimal amount) {
        log.info("[FieldUpdate] Updating optional fields for {} task(s) with status '{}'", taskNames.size(), STATUS_NEED_REVISION);

        FieldUpdateInfo updateInfo = new FieldUpdateInfo(monthOfProcess, yearOfProcess, paymentType, amount);
        List<Submission> submissionsToUpdate = new ArrayList<>();

        for (String taskName : taskNames) {
            processSubmissionForUpdate(taskName, updateInfo, submissionsToUpdate);
        }

        saveUpdatedSubmissions(submissionsToUpdate);
    }

    private void processSubmissionForUpdate(String taskName, FieldUpdateInfo updateInfo, List<Submission> submissionsToUpdate) {
        Optional<Submission> submissionOpt = submissionRepository.findByTaskName(taskName);
        if (submissionOpt.isEmpty()) {
            log.warn("[FieldUpdate] No submission found for task: {}", taskName);
            return;
        }

        Submission submission = submissionOpt.get();
        if (!STATUS_NEED_REVISION.equals(submission.getStatus())) {
            log.warn("[FieldUpdate] Skipping task {} - status is '{}', not '{}'",
                taskName, submission.getStatus(), STATUS_NEED_REVISION);
            return;
        }

        if (updateSubmissionFields(submission, updateInfo, taskName)) {
            submissionsToUpdate.add(submission);
        }
    }

    private boolean updateSubmissionFields(Submission submission, FieldUpdateInfo updateInfo, String taskName) {
        List<String> updates = new ArrayList<>();
        boolean hasUpdates = false;

        hasUpdates |= updateStringField(submission::getMonthOfProcess, submission::setMonthOfProcess,
            updateInfo.monthOfProcess(), "monthOfProcess", updates);
        hasUpdates |= updateStringField(submission::getYearOfProcess, submission::setYearOfProcess,
            updateInfo.yearOfProcess(), "yearOfProcess", updates);
        hasUpdates |= updateStringField(submission::getPaymentType, submission::setPaymentType,
            updateInfo.paymentType(), "paymentType", updates);
        hasUpdates |= updateAmountField(submission, updateInfo.amount(), updates);

        if (hasUpdates) {
            log.info("[FieldUpdate] Task {} updates: {}", taskName, String.join("; ", updates));
        } else {
            log.info("[FieldUpdate] No fields to update for task: {}", taskName);
        }

        return hasUpdates;
    }

    private boolean updateStringField(java.util.function.Supplier<String> getter, java.util.function.Consumer<String> setter,
                                     String newValue, String fieldName, List<String> updates) {
        if (newValue != null && !newValue.trim().isEmpty()) {
            String oldValue = getter.get();
            setter.accept(newValue);
            updates.add(String.format("%s: '%s' → '%s'", fieldName, oldValue, newValue));
            return true;
        }
        return false;
    }

    private boolean updateAmountField(Submission submission, BigDecimal newAmount, List<String> updates) {
        if (newAmount != null) {
            BigDecimal oldValue = submission.getAmount();
            submission.setAmount(newAmount);
            updates.add(String.format("amount: %s → %s", oldValue, newAmount));
            return true;
        }
        return false;
    }

    private void saveUpdatedSubmissions(List<Submission> submissionsToUpdate) {
        if (!submissionsToUpdate.isEmpty()) {
            submissionRepository.saveAll(submissionsToUpdate);
            log.info("[FieldUpdate] ✓ Successfully updated {} submission(s) with new field values", submissionsToUpdate.size());
        } else {
            log.info("[FieldUpdate] No submissions required field updates");
        }
    }

    private record FieldUpdateInfo(String monthOfProcess, String yearOfProcess, String paymentType, BigDecimal amount) {
    }

    private void processActionInternal(List<String> taskNames, String action, String reason, String currentUserNip) {
        List<Map<String, Object>> successResults = new ArrayList<>();
        List<Map<String, Object>> failedResults = new ArrayList<>();

        if (currentUserNip == null || currentUserNip.trim().isEmpty()) {
            throw new CustomBadRequestException(400, "Bad Request",
                    "Current user NIP is required but not provided");
        }

        log.info("[Action] Processing {} task(s) with action '{}' by user: {}",
                taskNames.size(), action, currentUserNip);

        for (String taskName : taskNames) {
            processIndividualTask(taskName, action, reason, currentUserNip, successResults, failedResults);
        }

        log.info("[Action] Processing completed. Success: {}, Failed: {}",
                successResults.size(), failedResults.size());

        if (!failedResults.isEmpty()) {
            List<String> failedTaskNames = failedResults.stream()
                    .map(result -> (String) result.get(KEY_TASK_NAME))
                    .filter(taskName -> taskName != null && !taskName.trim().isEmpty())
                    .toList();

            String errorMessage = "Failed to process " + failedResults.size() + " out of " + taskNames.size() + " task(s). " +
                    "All changes have been rolled back. Failed tasks: " + String.join(", ", failedTaskNames);

            log.error("[Action] ⚠️ TRANSACTION ROLLBACK TRIGGERED - All database changes will be reverted!");
            log.error("[Action] Failed task details:");
            for (Map<String, Object> failedResult : failedResults) {
                log.error("[Action] - Task: {}, Error: {}",
                        failedResult.get(KEY_TASK_NAME), failedResult.get(KEY_ERROR));
            }
            log.error("[Action] Transaction will be rolled back due to failures: {}", errorMessage);

            throw new CustomBadRequestException(500, "Transaction Failed", errorMessage);
        }

        log.info("[Action] All {} task(s) processed successfully", taskNames.size());
    }

    private void processIndividualTask(String taskName, String action, String reason, String currentUserNip,
                                     List<Map<String, Object>> successResults, List<Map<String, Object>> failedResults) {
        try {
            if (taskName == null || taskName.trim().isEmpty()) {
                addFailedResult(failedResults, taskName, "Task name is null or empty");
                return;
            }

            log.info("[ProcessTask] Processing task: {} with action: {}", taskName, action);
            TransactionActionRequest actionRequest = createActionRequest(taskName, action, reason, currentUserNip);

            log.info("[ProcessTask] Calling external approval service for task: {} with action: {}", taskName, action);
            ResponseEntity<?> actionResponse = approvalClient.action(actionRequest);

            log.info("[ProcessTask] External approval service response for task: {}, status: {}, body: {}",
                    taskName, actionResponse.getStatusCode(), actionResponse.getBody());

            if (actionResponse.getStatusCode().is2xxSuccessful()) {
                log.info("[ProcessTask] ✓ External approval service succeeded for task: {}, proceeding with database update", taskName);
                handleSuccessfulAction(taskName, action, reason, successResults);
            } else {
                log.error("[ProcessTask] ✗ External approval service failed for task: {}, status: {}", taskName, actionResponse.getStatusCode());
                handleFailedAction(taskName, action, actionResponse, failedResults);
            }

        } catch (Exception e) {
            log.error("[ProcessTask] ✗ Exception occurred while processing task: {} with action: {}", taskName, action, e);
            handleActionException(taskName, action, e, failedResults);
        }
    }

    private TransactionActionRequest createActionRequest(String taskName, String action, String reason, String currentUserNip) {
        TransactionActionRequest actionRequest = new TransactionActionRequest();
        actionRequest.setTaskName(taskName);
        actionRequest.setApproverCode(currentUserNip);
        actionRequest.setApproverUsername(currentUserNip);
        actionRequest.setAction(action);
        actionRequest.setReason(reason);

        log.info("[ActionRequest] Created action request for task {}", taskName);

        return actionRequest;
    }

    private void setSubmissionsStatusToPending(List<String> taskNames, String reason) {
        log.info("[PendingStatus] Setting status to Pending for {} task(s)", taskNames.size());

        for (String taskName : taskNames) {
            setSubmissionStatusToPending(taskName, reason);
        }

        log.info("[PendingStatus] Completed setting status to Pending for {} task(s)", taskNames.size());
    }

    private void setSubmissionStatusToPending(String taskName, String reason) {
        try {
            Optional<Submission> submissionOpt = submissionRepository.findByTaskName(taskName);
            if (submissionOpt.isEmpty()) {
                log.warn("[PendingStatus] No submission found for task: {}", taskName);
                return;
            }

            Submission submission = submissionOpt.get();
            String oldStatus = submission.getStatus();
            submission.setStatus(STATUS_PENDING);

            if (reason != null && !reason.trim().isEmpty()) {
                submission.setRemarks(reason);
            }

            submissionRepository.save(submission);
            log.info("[PendingStatus] ✓ Set submission status to Pending for task: {}, ID: {}, status: {} → {}",
                    taskName, submission.getId(), oldStatus, submission.getStatus());

        } catch (Exception e) {
            log.error("[PendingStatus] ✗ Error setting submission status to Pending for task: {}", taskName, e);
        }
    }



    private void handleSuccessfulAction(String taskName, String action, String reason, List<Map<String, Object>> successResults) {
        Map<String, Object> successItem = new HashMap<>();
        successItem.put(KEY_TASK_NAME, taskName);
        successItem.put(KEY_ACTION, action);
        successItem.put(KEY_STATUS, KEY_SUCCESS);
        successResults.add(successItem);

        log.info("[Action] ✓ Successfully processed action '{}' for task: {}", action, taskName);

        updateCurrentReviewer(taskName, action, reason);
    }

    private void handleFailedAction(String taskName, String action, ResponseEntity<?> actionResponse,
                                  List<Map<String, Object>> failedResults) {
        String errorMessage = "Action request failed with status: " + actionResponse.getStatusCode();
        addFailedResult(failedResults, taskName, errorMessage);

        log.error("[Action] ✗ Failed to process action '{}' for task: {}, status: {}",
                action, taskName, actionResponse.getStatusCode());
    }

    private void handleActionException(String taskName, String action, Exception e, List<Map<String, Object>> failedResults) {
        String errorMessage = "Exception occurred: " + e.getMessage();
        addFailedResult(failedResults, taskName, errorMessage);

        log.error("[Action] ✗ Exception processing action '{}' for task: {}", action, taskName, e);
    }

    private void addFailedResult(List<Map<String, Object>> failedResults, String taskName, String error) {
        Map<String, Object> failedItem = new HashMap<>();
        failedItem.put(KEY_TASK_NAME, taskName);
        failedItem.put(KEY_ERROR, error);
        failedResults.add(failedItem);
    }

    private void updateCurrentReviewer(String taskName, String action, String reason) {
        try {
            log.info("[ReviewerUpdate] Starting async update for task: {} with action: {}", taskName, action);

            Optional<Submission> submissionOpt = submissionRepository.findByTaskName(taskName);
            if (submissionOpt.isEmpty()) {
                log.warn("[ReviewerUpdate] No submission found for task: {}", taskName);
                return;
            }

            Submission submission = submissionOpt.get();
            String oldStatus = submission.getStatus();

            updateSubmissionStatusByAction(submission, action);

            if (reason != null && !reason.trim().isEmpty()) {
                submission.setRemarks(reason);
                log.info("[ReviewerUpdate] Updated remarks for task: {} with reason: {}", taskName, reason);
            }

            if (STATUS_WAITING_PAYMENT.equals(submission.getStatus())) {
                submission.setCurrentReviewer("");
                log.info("[ReviewerUpdate] ✓ Successfully updated completed submission for task: {}, ID: {}, " +
                        "action: {}, status: {} → {}, cleared reviewer (transaction complete)",
                        taskName, submission.getId(), action, oldStatus, submission.getStatus());
            } else {
                updateSubmissionWithCurrentReviewer(submission);
                log.info("[ReviewerUpdate] ✓ Successfully updated submission for task: {}, ID: {}, " +
                        "action: {}, status: {} → {}, reviewer updated",
                        taskName, submission.getId(), action, oldStatus, submission.getStatus());
            }

            log.info("[ReviewerUpdate] About to save submission to database - task: {}, ID: {}, status: {}",
                    taskName, submission.getId(), submission.getStatus());
            Submission savedSubmission = submissionRepository.save(submission);
            log.info("[ReviewerUpdate] ✓ Successfully saved submission to database - task: {}, ID: {}, final status: {}",
                    taskName, savedSubmission.getId(), savedSubmission.getStatus());

        } catch (Exception e) {
            log.error("[ReviewerUpdate] ✗ Error updating submission for task: {} with action: {}", taskName, action, e);
        }
    }
    
    private boolean isCurrentUserPayroll() {
        List<String> roles = SecurityUtil.getCurrentUserRole();

        for (String role : roles) {
            log.info("[isCurrentUserPayroll] - Role: {}", role);
        }

        boolean hasPayrollRole = roles.stream()
                .anyMatch(role -> role.equals("ROLE_ADMIN_HRPAYROLL") || role.equals("ROLE_ADMIN_HRPAYROLL_HEAD"));

        log.info("[isCurrentUserPayroll] Result: User {} payroll role", hasPayrollRole ? "HAS" : "DOES NOT HAVE");
        return hasPayrollRole;
    }

    private String submitToApproval(String submitterNIP, String submitterName) {
        boolean hasPayrollRole = isCurrentUserPayroll();
        int isPayroll = hasPayrollRole ? 1 : 0;
        log.info("[SubmitToApproval] Name: {}, NIP: {}, isPayroll={}",
                 submitterName, submitterNIP, isPayroll);

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nip", submitterNIP);
        parameters.put("isPayroll", isPayroll);

        SubmitTransactionRequest transactionRequest = SubmitTransactionRequest.builder()
                .ruleCode(staggeredPaymentRuleCode)
                .systemCode(staggeredPaymentSystemCode)
                .parameter(parameters)
                .createdBy(submitterNIP)
                .createdByName(submitterName)
                .processName(staggeredPaymentProcessName)
                .build();

        ResponseEntity<ApiResponse> responseEntity = approvalClient.submitTransaction(transactionRequest);
        ApiResponse apiResponse = responseEntity.getBody();

        if (apiResponse != null && apiResponse.isSuccess()) {
            Map<String, Object> responseData = (Map<String, Object>) apiResponse.getData();
            return (String) responseData.get(KEY_TASK_NAME);
        } else {
            String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Unknown error";
            log.error("Failed to submit transaction: {}", errorMessage);
            return null;
        }
    }

    @Async
    public void updateStatus(List<Submission> submissions, String submitterNIP, String submitterName) {
        for (Submission submission : submissions) {
            try {
                String taskName = submitToApproval(submitterNIP, submitterName);

                if (taskName != null) {
                    submission.setTaskName(taskName);
                    submission.setStatus(STATUS_WAITING_APPROVAL);

                    updateSubmissionWithCurrentReviewer(submission);

                    submissionRepository.save(submission);
                    log.info("Approval process initiated successfully for {}, ID: {} with current reviewer updated", submission.getNip(), submission.getId());
                } else {
                    submission.setStatus(STATUS_FAILED);
                    submissionRepository.save(submission);
                    log.error("Failed to submit transaction for approval for {}", submission.getNip());
                }
            } catch (Exception e) {
                log.error("Error processing approval for {}", submission.getNip(), e);
                submission.setStatus(STATUS_FAILED);
                submissionRepository.save(submission);
            }
        }
    }


    private void updateSubmissionStatusByAction(Submission submission, String action) {
        if (action == null || action.trim().isEmpty()) {
            log.warn("[StatusUpdate] Action is null or empty for submission ID: {}, keeping current status",
                    submission.getId());
            return;
        }

        try {
            if (submission.getTaskName() != null && !submission.getTaskName().isEmpty()) {
                ApiResponse detailResponse = approvalClient.detail(submission.getTaskName());
                if (detailResponse != null && detailResponse.isSuccess() && detailResponse.getData() != null) {
                    ObjectMapper mapper = new ObjectMapper();
                    mapper.registerModule(new JavaTimeModule());
                    DetailResponse detail = mapper.convertValue(detailResponse.getData(), DetailResponse.class);

                    if (detail != null && "complete".equals(detail.getStatusCode())) {
                        handleCompleteStatus(submission);
                        return;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("[StatusUpdate] Error checking statusCode for submission ID: {}, proceeding with action-based status update", submission.getId(), e);
        }

        String normalizedAction = action.trim().toUpperCase();
        String newStatus;

        switch (normalizedAction) {
            case ACTION_APPROVE:
                boolean isPaymentTask = isTaskFromPaymentTable(submission.getTaskName());
                if (isPaymentTask) {
                    newStatus = STATUS_WAITING_PAYMENT_APPROVAL;
                    log.info("[StatusUpdate] Action APPROVE (Payment Task): setting status to '{}' for submission ID: {}",
                            newStatus, submission.getId());
                } else {
                    newStatus = STATUS_WAITING_APPROVAL;
                    log.info("[StatusUpdate] Action APPROVE (Submission Task): setting status to '{}' for submission ID: {}",
                            newStatus, submission.getId());
                }
                break;

            case ACTION_REJECT:
                newStatus = STATUS_DECLINED;
                log.info("[StatusUpdate] Action REJECT: changing status to '{}' for submission ID: {}",
                        newStatus, submission.getId());
                break;

            case ACTION_REVISE:
                newStatus = STATUS_NEED_REVISION;
                log.info("[StatusUpdate] Action REVISE: changing status to '{}' for submission ID: {}",
                        newStatus, submission.getId());
                break;

            case ACTION_CANCEL:
                newStatus = STATUS_CANCELLED;
                log.info("[StatusUpdate] Action CANCEL: changing status from '{}' to '{}' for submission ID: {}",
                        submission.getStatus(), newStatus, submission.getId());
                break;

            default:
                log.warn("[StatusUpdate] Unknown action '{}' for submission ID: {}, keeping current status '{}'",
                        action, submission.getId(), submission.getStatus());
                return;
        }

        submission.setStatus(newStatus);
    }


    private void handleCompleteStatus(Submission submission) {
        boolean isPaymentTask = isTaskFromPaymentTable(submission.getTaskName());

        if (isPaymentTask) {
            try {
                Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(submission.getReferenceNumber());
                if (paymentOpt.isPresent()) {
                    SubmissionPayment submissionPayment = paymentOpt.get();
                    if (submissionPayment.getPaymentDate() != null) {
                        submission.setStatus(STATUS_PAID);
                        log.info("[StatusUpdate] Transaction complete from payment task with payment date, setting status to 'Paid' for submission ID: {}", submission.getId());
                    } else {
                        submission.setStatus(STATUS_UNPAID);
                        log.info("[StatusUpdate] Transaction complete from payment task without payment date, setting status to 'Unpaid' for submission ID: {}", submission.getId());
                    }
                } else {
                    submission.setStatus(STATUS_UNPAID);
                    log.warn("[StatusUpdate] Transaction complete from payment task but no payment record found, setting status to 'Unpaid' for submission ID: {}", submission.getId());
                }
            } catch (Exception e) {
                log.error("[StatusUpdate] Error checking payment date for submission ID: {}, defaulting to 'Unpaid'", submission.getId(), e);
                submission.setStatus(STATUS_UNPAID);
            }
        } else {
            submission.setStatus(STATUS_WAITING_PAYMENT);
            log.info("[StatusUpdate] Transaction complete from submission task, setting status to 'Waiting for Payment' for submission ID: {}", submission.getId());
        }
    }

    private boolean isTaskFromPaymentTable(String taskName) {
        if (taskName == null || taskName.trim().isEmpty()) {
            return false;
        }

        try {
            boolean exists = submissionPaymentRepository.existsByTaskName(taskName);

            log.info("[TaskSourceCheck] TaskName '{}' {} in submission_payment table",
                    taskName, exists ? "found" : "not found");

            return exists;
        } catch (Exception e) {
            log.error("[TaskSourceCheck] Error checking if taskName '{}' exists in submission_payment table", taskName, e);
            return false;
        }
    }

    private void updateSubmissionWithCurrentReviewer(Submission submission) {
        try {
            if (submission.getTaskName() == null || submission.getTaskName().isEmpty()) {
                log.warn("Cannot update reviewer info: taskName is null or empty for submission ID: {}", submission.getId());
                return;
            }

            ApiResponse response = approvalClient.detail(submission.getTaskName());

            if (response == null || !response.isSuccess() || response.getData() == null) {
                String errorMsg = "Error getting approval details for submission ID " + submission.getId() + ": " +
                        (response != null ? response.getMessage() : "Null response");
                log.error(errorMsg);
                submission.setCurrentReviewer("");
                return;
            }

            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());
            DetailResponse detailResponse = mapper.convertValue(response.getData(), DetailResponse.class);

            if (detailResponse == null) {
                submission.setCurrentReviewer("");
                return;
            }

            submission.setCurrentReviewer("");
            extractAndSetApproverInfo(submission, detailResponse, mapper);

        } catch (Exception e) {
            String errorMsg = "Error updating submission with approval details for ID " + submission.getId() + ": " + e.getMessage();
            log.error(errorMsg);
            submission.setCurrentReviewer("");
        }
    }

    public String submitToApprovalWithConfig(String submitterNIP, String submitterName, String ruleCode,
                                           String systemCode, String processName, Map<String, Object> parameters) {
        log.info("[ApprovalService] Submitting transaction with custom config - Rule: {}, System: {}, Process: {}",
                ruleCode, systemCode, processName);

        SubmitTransactionRequest transactionRequest = SubmitTransactionRequest.builder()
                .ruleCode(ruleCode)
                .systemCode(systemCode)
                .parameter(parameters)
                .createdBy(submitterNIP)
                .createdByName(submitterName)
                .processName(processName)
                .build();

        try {
            ResponseEntity<ApiResponse> responseEntity = approvalClient.submitTransaction(transactionRequest);
            ApiResponse apiResponse = responseEntity.getBody();

            if (apiResponse != null && apiResponse.isSuccess()) {
                Map<String, Object> responseData = (Map<String, Object>) apiResponse.getData();
                String taskName = (String) responseData.get(KEY_TASK_NAME);
                log.info("[ApprovalService] Successfully submitted transaction, taskName: {}", taskName);
                return taskName;
            } else {
                String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Unknown error";
                log.error("[ApprovalService] Failed to submit transaction: {}", errorMessage);
                return null;
            }
        } catch (Exception e) {
            log.error("[ApprovalService] Exception during transaction submission", e);
            return null;
        }
    }

    public void updateCurrentReviewerForTask(String taskName, String entityId,
                                           java.util.function.Consumer<String> setCurrentReviewer) {
        try {
            if (taskName == null || taskName.isEmpty()) {
                log.warn("[ApprovalService] Cannot update reviewer info: taskName is null or empty for entity: {}", entityId);
                return;
            }

            ApiResponse response = approvalClient.detail(taskName);

            if (response == null || !response.isSuccess() || response.getData() == null) {
                String errorMsg = "Error getting approval details for entity " + entityId + ": " +
                        (response != null ? response.getMessage() : "Null response");
                log.error(errorMsg);
                setCurrentReviewer.accept("");
                return;
            }

            ObjectMapper mapper = new ObjectMapper();
            mapper.registerModule(new JavaTimeModule());
            DetailResponse detailResponse = mapper.convertValue(response.getData(), DetailResponse.class);

            if (detailResponse == null) {
                setCurrentReviewer.accept("");
                return;
            }

            setCurrentReviewer.accept("");
            String approversJson = extractApproverInfo(detailResponse, mapper, entityId);
            setCurrentReviewer.accept(approversJson);

        } catch (Exception e) {
            String errorMsg = "Error updating entity with approval details for " + entityId + ": " + e.getMessage();
            log.error(errorMsg);
            setCurrentReviewer.accept("");
        }
    }

    private String extractApproverInfo(DetailResponse detailResponse, ObjectMapper mapper, String entityId) {
        if (detailResponse.getCurrentLayerState() == null) {
            return "";
        }

        try {
            List<Map<String, Object>> approvers = (List<Map<String, Object>>) detailResponse.getCurrentLayerState().get("approvers");

            if (approvers == null || approvers.isEmpty()) {
                return "";
            }

            List<Map<String, Object>> simplifiedApprovers = new ArrayList<>();

            for (Map<String, Object> approver : approvers) {
                Map<String, Object> simplifiedApprover = new HashMap<>();

                simplifiedApprover.put("approverCode", approver.get("approverCode"));
                simplifiedApprover.put("approverNik", approver.get("approverNik"));
                simplifiedApprover.put("approverName", approver.get("approverName"));
                simplifiedApprover.put("approverJob", approver.get("approverJob"));

                simplifiedApprovers.add(simplifiedApprover);
            }

            String approversJson = mapper.writeValueAsString(simplifiedApprovers);
            String currentApproverCode = (String) detailResponse.getCurrentLayerState().get("code");

            log.info("[ApprovalService] Updated current reviewer with simplified JSON data for {} approvers, Code={}, Entity: {}",
                    approvers.size(), currentApproverCode, entityId);

            return approversJson;

        } catch (Exception e) {
            log.warn("[ApprovalService] Error extracting reviewer info from currentLayerState for entity {}: {}",
                    entityId, e.getMessage());
            return "";
        }
    }

    private void extractAndSetApproverInfo(Submission submission, DetailResponse detailResponse, ObjectMapper mapper) {
        if (detailResponse.getCurrentLayerState() == null) {
            return;
        }

        try {
            List<Map<String, Object>> approvers = (List<Map<String, Object>>) detailResponse.getCurrentLayerState().get("approvers");

            if (approvers == null || approvers.isEmpty()) {
                return;
            }

            List<Map<String, Object>> simplifiedApprovers = new ArrayList<>();

            for (Map<String, Object> approver : approvers) {
                Map<String, Object> simplifiedApprover = new HashMap<>();

                simplifiedApprover.put("approverCode", approver.get("approverCode"));
                simplifiedApprover.put("approverNik", approver.get("approverNik"));
                simplifiedApprover.put("approverName", approver.get("approverName"));
                simplifiedApprover.put("approverJob", approver.get("approverJob"));

                simplifiedApprovers.add(simplifiedApprover);
            }

            String approversJson = mapper.writeValueAsString(simplifiedApprovers);
            submission.setCurrentReviewer(approversJson);

            String currentApproverCode = (String) detailResponse.getCurrentLayerState().get("code");

            log.info("Updated current reviewer with simplified JSON data for {} approvers, Code={}, Submission ID: {}",
                    approvers.size(), currentApproverCode, submission.getId());

        } catch (Exception e) {
            log.warn("Error extracting reviewer info from currentLayerState for submission ID {}: {}",
                    submission.getId(), e.getMessage());
        }
    }

}
